#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片包分配器
根据Excel配置文件将barcode文件夹下的图片按比例分配到不同的package中
确保每张图片只分配到一个package中
"""

import pandas as pd
import os
import shutil
import random
from pathlib import Path
from typing import Dict, List, Tuple
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ImagePackageDistributor:
    def __init__(self, excel_file: str, barcode_folder: str, output_folder: str = "packages"):
        """
        初始化图片包分配器
        
        Args:
            excel_file: Excel配置文件路径
            barcode_folder: 包含图片的barcode文件夹路径
            output_folder: 输出文件夹路径
        """
        self.excel_file = excel_file
        self.barcode_folder = barcode_folder
        self.output_folder = output_folder
        self.config_df = None
        self.package_names = []
        self.folder_mapping = {}
        
    def load_config(self):
        """加载Excel配置文件"""
        try:
            # 读取第二个sheet，这里包含package配置
            self.config_df = pd.read_excel(self.excel_file, sheet_name='1000+200（1333）', header=None)
            logger.info(f"成功加载配置文件，数据形状: {self.config_df.shape}")
            
            # 获取package名称（第一行，从第4列开始）
            self.package_names = []
            for col_idx in range(3, self.config_df.shape[1]):  # 从第4列开始
                package_name = self.config_df.iloc[0, col_idx]
                if pd.notna(package_name):
                    self.package_names.append(str(package_name))
            
            logger.info(f"找到的package名称: {self.package_names}")
            
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            raise
    
    def build_folder_mapping(self):
        """构建文件夹名称映射"""
        # 获取barcode文件夹下的所有子文件夹
        barcode_folders = [f for f in os.listdir(self.barcode_folder) 
                          if os.path.isdir(os.path.join(self.barcode_folder, f))]
        
        # 构建映射关系：库区别名 -> 实际文件夹名
        self.folder_mapping = {}
        
        for folder_name in barcode_folders:
            # 找到第一个'-'符号，忽略前面的部分
            if '-' in folder_name:
                suffix = folder_name[folder_name.index('-') + 1:]
                self.folder_mapping[suffix] = folder_name
        
        logger.info(f"构建文件夹映射完成，共{len(self.folder_mapping)}个映射")
        
    def get_images_from_folder(self, folder_path: str) -> List[str]:
        """获取文件夹中的所有图片文件"""
        image_extensions = {'.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.gif'}
        images = []
        
        if os.path.exists(folder_path):
            for file in os.listdir(folder_path):
                if any(file.lower().endswith(ext) for ext in image_extensions):
                    images.append(os.path.join(folder_path, file))
        
        return images
    
    def sanitize_folder_name(self, name: str) -> str:
        """清理文件夹名称，替换不合法的字符"""
        # 替换不合法的文件夹名称字符
        illegal_chars = ['/', '\\', ':', '*', '?', '"', '<', '>', '|']
        sanitized = name
        for char in illegal_chars:
            sanitized = sanitized.replace(char, '_')
        return sanitized

    def distribute_images(self):
        """分配图片到各个package"""
        # 创建输出文件夹
        os.makedirs(self.output_folder, exist_ok=True)

        # 为每个package创建文件夹
        for package_name in self.package_names:
            safe_package_name = self.sanitize_folder_name(package_name)
            package_folder = os.path.join(self.output_folder, safe_package_name)
            os.makedirs(package_folder, exist_ok=True)
        
        # 统计信息
        total_images_processed = 0
        distribution_summary = {package: 0 for package in self.package_names}
        
        # 遍历配置中的每一行（从第3行开始，跳过标题行）
        for row_idx in range(2, len(self.config_df)):
            area_name = self.config_df.iloc[row_idx, 0]  # 库区别名
            
            if pd.isna(area_name):
                continue
                
            area_name = str(area_name)
            
            # 检查是否有对应的文件夹
            if area_name not in self.folder_mapping:
                logger.warning(f"未找到库区 {area_name} 对应的文件夹")
                continue
            
            actual_folder_name = self.folder_mapping[area_name]
            folder_path = os.path.join(self.barcode_folder, actual_folder_name)
            
            # 获取该文件夹中的所有图片
            images = self.get_images_from_folder(folder_path)
            if not images:
                logger.warning(f"文件夹 {actual_folder_name} 中没有找到图片")
                continue
            
            # 随机打乱图片列表，确保随机分配
            random.shuffle(images)
            
            logger.info(f"处理库区 {area_name} ({actual_folder_name})，共 {len(images)} 张图片")
            
            # 获取每个package需要的图片数量
            image_index = 0
            for col_idx, package_name in enumerate(self.package_names):
                package_count = self.config_df.iloc[row_idx, col_idx + 3]  # 从第4列开始
                
                if pd.isna(package_count) or package_count == 0:
                    continue
                
                # 转换为整数
                try:
                    count = int(float(package_count))
                except (ValueError, TypeError):
                    logger.warning(f"无法解析数量: {package_count}")
                    continue
                
                if count <= 0:
                    continue
                
                # 检查是否有足够的图片
                if image_index + count > len(images):
                    available = len(images) - image_index
                    logger.warning(f"库区 {area_name} package {package_name} 需要 {count} 张图片，但只剩 {available} 张")
                    count = available
                
                if count <= 0:
                    break
                
                # 复制图片到对应的package文件夹
                safe_package_name = self.sanitize_folder_name(package_name)
                package_folder = os.path.join(self.output_folder, safe_package_name)
                
                for i in range(count):
                    if image_index >= len(images):
                        break
                        
                    src_image = images[image_index]
                    image_filename = os.path.basename(src_image)
                    
                    # 为了避免文件名冲突，添加库区前缀
                    new_filename = f"{area_name}_{image_filename}"
                    dst_image = os.path.join(package_folder, new_filename)
                    
                    try:
                        shutil.copy2(src_image, dst_image)
                        image_index += 1
                        distribution_summary[package_name] += 1
                        total_images_processed += 1
                    except Exception as e:
                        logger.error(f"复制图片失败: {src_image} -> {dst_image}, 错误: {e}")
                
                logger.info(f"  -> {package_name}: 分配了 {count} 张图片")
        
        # 输出统计信息
        logger.info("=" * 50)
        logger.info("分配完成！统计信息:")
        logger.info(f"总共处理图片: {total_images_processed} 张")
        for package_name, count in distribution_summary.items():
            logger.info(f"  {package_name}: {count} 张图片")
        logger.info("=" * 50)
        
        return distribution_summary
    
    def run(self):
        """运行图片分配流程"""
        logger.info("开始图片包分配流程...")
        
        # 1. 加载配置
        self.load_config()
        
        # 2. 构建文件夹映射
        self.build_folder_mapping()
        
        # 3. 分配图片
        summary = self.distribute_images()
        
        logger.info("图片包分配流程完成！")
        return summary


def main():
    """主函数"""
    # 配置参数
    excel_file = "常州1600标签.xlsx"
    barcode_folder = "barcode"
    output_folder = "packages"
    
    # 检查文件是否存在
    if not os.path.exists(excel_file):
        print(f"错误: Excel文件 {excel_file} 不存在")
        return
    
    if not os.path.exists(barcode_folder):
        print(f"错误: barcode文件夹 {barcode_folder} 不存在")
        return
    
    # 创建分配器并运行
    distributor = ImagePackageDistributor(excel_file, barcode_folder, output_folder)
    
    try:
        summary = distributor.run()
        print("\n分配结果:")
        for package_name, count in summary.items():
            print(f"  {package_name}: {count} 张图片")
            
    except Exception as e:
        logger.error(f"运行失败: {e}")
        raise


if __name__ == "__main__":
    main()
